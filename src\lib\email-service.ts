// Email service for Tennis Whisperer
// This service handles all automated email communications

interface EmailTemplate {
  subject: string;
  html: string;
  text: string;
}

interface OrderEmailData {
  orderId: string;
  customerName: string;
  customerEmail: string;
  orderTotal: number;
  items: Array<{
    name: string;
    quantity: number;
    price: number;
  }>;
  shippingAddress: string;
  estimatedDelivery: string;
}

interface SessionEmailData {
  studentName: string;
  mentorName: string;
  sessionDate: string;
  sessionTime: string;
  sessionType: string;
  meetingLink?: string;
}

class EmailService {
  private apiKey: string;
  private fromEmail: string;
  private fromName: string;

  constructor() {
    this.apiKey = process.env.RESEND_API_KEY || '';
    this.fromEmail = process.env.FROM_EMAIL || '<EMAIL>';
    this.fromName = 'Tennis Whisperer';
  }

  /**
   * Send order confirmation email
   */
  async sendOrderConfirmation(data: OrderEmailData): Promise<boolean> {
    const template = this.getOrderConfirmationTemplate(data);
    
    return this.sendEmail({
      to: data.customerEmail,
      subject: template.subject,
      html: template.html,
      text: template.text
    });
  }

  /**
   * Send order status update email
   */
  async sendOrderStatusUpdate(
    customerEmail: string,
    customerName: string,
    orderId: string,
    status: string,
    trackingNumber?: string
  ): Promise<boolean> {
    const template = this.getOrderStatusTemplate(customerName, orderId, status, trackingNumber);
    
    return this.sendEmail({
      to: customerEmail,
      subject: template.subject,
      html: template.html,
      text: template.text
    });
  }

  /**
   * Send session reminder email
   */
  async sendSessionReminder(
    studentEmail: string,
    data: SessionEmailData
  ): Promise<boolean> {
    const template = this.getSessionReminderTemplate(data);
    
    return this.sendEmail({
      to: studentEmail,
      subject: template.subject,
      html: template.html,
      text: template.text
    });
  }

  /**
   * Send session cancellation email
   */
  async sendSessionCancellation(
    studentEmail: string,
    data: SessionEmailData,
    reason?: string
  ): Promise<boolean> {
    const template = this.getSessionCancellationTemplate(data, reason);
    
    return this.sendEmail({
      to: studentEmail,
      subject: template.subject,
      html: template.html,
      text: template.text
    });
  }

  /**
   * Send welcome email for new mentorship students
   */
  async sendMentorshipWelcome(
    studentEmail: string,
    studentName: string,
    programName: string,
    mentorName: string
  ): Promise<boolean> {
    const template = this.getMentorshipWelcomeTemplate(studentName, programName, mentorName);
    
    return this.sendEmail({
      to: studentEmail,
      subject: template.subject,
      html: template.html,
      text: template.text
    });
  }

  /**
   * Core email sending function
   */
  private async sendEmail(params: {
    to: string;
    subject: string;
    html: string;
    text: string;
  }): Promise<boolean> {
    try {
      // In a real implementation, you would use a service like Resend, SendGrid, or AWS SES
      // For now, we'll log the email and return true
      console.log('📧 Email would be sent:', {
        to: params.to,
        subject: params.subject,
        from: `${this.fromName} <${this.fromEmail}>`,
        preview: params.text.substring(0, 100) + '...'
      });

      // Uncomment and configure when ready to send real emails:
      /*
      const response = await fetch('https://api.resend.com/emails', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          from: `${this.fromName} <${this.fromEmail}>`,
          to: params.to,
          subject: params.subject,
          html: params.html,
          text: params.text,
        }),
      });

      return response.ok;
      */

      return true; // Simulate success for development
    } catch (error) {
      console.error('Error sending email:', error);
      return false;
    }
  }

  /**
   * Order confirmation email template
   */
  private getOrderConfirmationTemplate(data: OrderEmailData): EmailTemplate {
    const itemsHtml = data.items.map(item => `
      <tr>
        <td style="padding: 8px; border-bottom: 1px solid #eee;">${item.name}</td>
        <td style="padding: 8px; border-bottom: 1px solid #eee; text-align: center;">${item.quantity}</td>
        <td style="padding: 8px; border-bottom: 1px solid #eee; text-align: right;">R${item.price.toFixed(2)}</td>
      </tr>
    `).join('');

    const itemsText = data.items.map(item => 
      `${item.name} x${item.quantity} - R${item.price.toFixed(2)}`
    ).join('\n');

    return {
      subject: `Order Confirmation #${data.orderId} - Tennis Whisperer`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h1 style="color: #16a34a;">Order Confirmed!</h1>
          <p>Hi ${data.customerName},</p>
          <p>Thank you for your order! We've received your order and are preparing it for shipment.</p>
          
          <div style="background: #f9f9f9; padding: 20px; margin: 20px 0; border-radius: 8px;">
            <h3>Order Details</h3>
            <p><strong>Order Number:</strong> #${data.orderId}</p>
            <p><strong>Estimated Delivery:</strong> ${data.estimatedDelivery}</p>
          </div>

          <table style="width: 100%; border-collapse: collapse; margin: 20px 0;">
            <thead>
              <tr style="background: #f5f5f5;">
                <th style="padding: 12px; text-align: left; border-bottom: 2px solid #ddd;">Item</th>
                <th style="padding: 12px; text-align: center; border-bottom: 2px solid #ddd;">Qty</th>
                <th style="padding: 12px; text-align: right; border-bottom: 2px solid #ddd;">Price</th>
              </tr>
            </thead>
            <tbody>
              ${itemsHtml}
              <tr style="font-weight: bold; background: #f9f9f9;">
                <td colspan="2" style="padding: 12px; border-top: 2px solid #ddd;">Total</td>
                <td style="padding: 12px; text-align: right; border-top: 2px solid #ddd;">R${data.orderTotal.toFixed(2)}</td>
              </tr>
            </tbody>
          </table>

          <p><strong>Shipping Address:</strong><br>${data.shippingAddress}</p>
          
          <p>We'll send you another email with tracking information once your order ships.</p>
          
          <p>Best regards,<br>The Tennis Whisperer Team</p>
        </div>
      `,
      text: `
Order Confirmed! #${data.orderId}

Hi ${data.customerName},

Thank you for your order! We've received your order and are preparing it for shipment.

Order Details:
- Order Number: #${data.orderId}
- Estimated Delivery: ${data.estimatedDelivery}

Items:
${itemsText}

Total: R${data.orderTotal.toFixed(2)}

Shipping Address:
${data.shippingAddress}

We'll send you another email with tracking information once your order ships.

Best regards,
The Tennis Whisperer Team
      `
    };
  }

  /**
   * Order status update email template
   */
  private getOrderStatusTemplate(
    customerName: string,
    orderId: string,
    status: string,
    trackingNumber?: string
  ): EmailTemplate {
    const statusMessages = {
      'processing': 'Your order is being processed',
      'shipped': 'Your order has been shipped',
      'delivered': 'Your order has been delivered',
      'cancelled': 'Your order has been cancelled'
    };

    const message = statusMessages[status as keyof typeof statusMessages] || `Order status updated to ${status}`;
    const trackingInfo = trackingNumber ? `\n\nTracking Number: ${trackingNumber}` : '';

    return {
      subject: `Order Update #${orderId} - ${message}`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h1 style="color: #16a34a;">Order Update</h1>
          <p>Hi ${customerName},</p>
          <p>${message}.</p>
          
          <div style="background: #f9f9f9; padding: 20px; margin: 20px 0; border-radius: 8px;">
            <p><strong>Order Number:</strong> #${orderId}</p>
            <p><strong>Status:</strong> ${status.charAt(0).toUpperCase() + status.slice(1)}</p>
            ${trackingNumber ? `<p><strong>Tracking Number:</strong> ${trackingNumber}</p>` : ''}
          </div>
          
          <p>Best regards,<br>The Tennis Whisperer Team</p>
        </div>
      `,
      text: `
Order Update #${orderId}

Hi ${customerName},

${message}.

Order Number: #${orderId}
Status: ${status.charAt(0).toUpperCase() + status.slice(1)}${trackingInfo}

Best regards,
The Tennis Whisperer Team
      `
    };
  }

  /**
   * Session reminder email template
   */
  private getSessionReminderTemplate(data: SessionEmailData): EmailTemplate {
    return {
      subject: `Session Reminder - ${data.sessionDate} with ${data.mentorName}`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h1 style="color: #16a34a;">Session Reminder</h1>
          <p>Hi ${data.studentName},</p>
          <p>This is a reminder about your upcoming tennis session.</p>
          
          <div style="background: #f9f9f9; padding: 20px; margin: 20px 0; border-radius: 8px;">
            <h3>Session Details</h3>
            <p><strong>Date:</strong> ${data.sessionDate}</p>
            <p><strong>Time:</strong> ${data.sessionTime}</p>
            <p><strong>Mentor:</strong> ${data.mentorName}</p>
            <p><strong>Type:</strong> ${data.sessionType}</p>
            ${data.meetingLink ? `<p><strong>Meeting Link:</strong> <a href="${data.meetingLink}">${data.meetingLink}</a></p>` : ''}
          </div>
          
          <p>We look forward to seeing you!</p>
          
          <p>Best regards,<br>The Tennis Whisperer Team</p>
        </div>
      `,
      text: `
Session Reminder

Hi ${data.studentName},

This is a reminder about your upcoming tennis session.

Session Details:
- Date: ${data.sessionDate}
- Time: ${data.sessionTime}
- Mentor: ${data.mentorName}
- Type: ${data.sessionType}
${data.meetingLink ? `- Meeting Link: ${data.meetingLink}` : ''}

We look forward to seeing you!

Best regards,
The Tennis Whisperer Team
      `
    };
  }

  /**
   * Session cancellation email template
   */
  private getSessionCancellationTemplate(data: SessionEmailData, reason?: string): EmailTemplate {
    return {
      subject: `Session Cancelled - ${data.sessionDate} with ${data.mentorName}`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h1 style="color: #dc2626;">Session Cancelled</h1>
          <p>Hi ${data.studentName},</p>
          <p>We regret to inform you that your tennis session has been cancelled.</p>
          
          <div style="background: #fef2f2; padding: 20px; margin: 20px 0; border-radius: 8px; border-left: 4px solid #dc2626;">
            <h3>Cancelled Session Details</h3>
            <p><strong>Date:</strong> ${data.sessionDate}</p>
            <p><strong>Time:</strong> ${data.sessionTime}</p>
            <p><strong>Mentor:</strong> ${data.mentorName}</p>
            ${reason ? `<p><strong>Reason:</strong> ${reason}</p>` : ''}
          </div>
          
          <p>Please contact us to reschedule your session. We apologize for any inconvenience.</p>
          
          <p>Best regards,<br>The Tennis Whisperer Team</p>
        </div>
      `,
      text: `
Session Cancelled

Hi ${data.studentName},

We regret to inform you that your tennis session has been cancelled.

Cancelled Session Details:
- Date: ${data.sessionDate}
- Time: ${data.sessionTime}
- Mentor: ${data.mentorName}
${reason ? `- Reason: ${reason}` : ''}

Please contact us to reschedule your session. We apologize for any inconvenience.

Best regards,
The Tennis Whisperer Team
      `
    };
  }

  /**
   * Mentorship welcome email template
   */
  private getMentorshipWelcomeTemplate(
    studentName: string,
    programName: string,
    mentorName: string
  ): EmailTemplate {
    return {
      subject: `Welcome to ${programName} - Tennis Whisperer`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h1 style="color: #16a34a;">Welcome to Tennis Whisperer!</h1>
          <p>Hi ${studentName},</p>
          <p>Welcome to the ${programName}! We're excited to have you on board.</p>
          
          <div style="background: #f0fdf4; padding: 20px; margin: 20px 0; border-radius: 8px; border-left: 4px solid #16a34a;">
            <h3>Your Program Details</h3>
            <p><strong>Program:</strong> ${programName}</p>
            <p><strong>Your Mentor:</strong> ${mentorName}</p>
          </div>
          
          <p>Your mentor will be in touch soon to schedule your first session. In the meantime, you can access your student dashboard to:</p>
          
          <ul>
            <li>View your progress</li>
            <li>Access learning resources</li>
            <li>Schedule sessions</li>
            <li>Chat with your mentor</li>
          </ul>
          
          <p><a href="${process.env.NEXT_PUBLIC_BASE_URL}/student-dashboard" style="background: #16a34a; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">Access Your Dashboard</a></p>
          
          <p>Best regards,<br>The Tennis Whisperer Team</p>
        </div>
      `,
      text: `
Welcome to Tennis Whisperer!

Hi ${studentName},

Welcome to the ${programName}! We're excited to have you on board.

Your Program Details:
- Program: ${programName}
- Your Mentor: ${mentorName}

Your mentor will be in touch soon to schedule your first session. In the meantime, you can access your student dashboard to:

- View your progress
- Access learning resources
- Schedule sessions
- Chat with your mentor

Access Your Dashboard: ${process.env.NEXT_PUBLIC_BASE_URL}/student-dashboard

Best regards,
The Tennis Whisperer Team
      `
    };
  }
}

export const emailService = new EmailService();
