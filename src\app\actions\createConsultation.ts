'use server';

import { revalidatePath } from 'next/cache';
import { createClient } from '@/utils/supabase/server';
import { createServiceRoleClient } from '@/utils/supabase/service';
import { consultationSchema, type ConsultationFormValues } from '@/schemas/consultation';
import { v4 as uuidv4 } from 'uuid';

/**
 * Server action to create a new consultation booking
 * Handles form submission and stores data in Supabase
 */
export async function createConsultation(data: ConsultationFormValues) {
  try {
    // Format the date as ISO string for storage
    const formattedDate = data.date.toISOString().split('T')[0];
    
    // Prepare the consultation data
    const consultationData = {
      first_name: data.firstName,
      last_name: data.lastName,
      email: data.email,
      phone_number: data.phoneNumber,
      location: data.location,
      scheduled_date: formattedDate,
      scheduled_time: data.time,
      reason: data.reason,
      status: 'pending',
      created_at: new Date().toISOString(),
    };
    
    // Validate with zod schema (throws if invalid)
    consultationSchema.parse(consultationData);
    
    try {
      // Create Supabase clients
      const supabase = await createClient();
      const serviceSupabase = createServiceRoleClient();

      // Step 1: Check if user already exists with this email
      const { data: existingUser } = await serviceSupabase.auth.admin.listUsers();
      const userExists = existingUser?.users?.find(user => user.email === data.email);

      let userId = null;

      if (!userExists) {
        // Step 2: Create a new user account with student role
        console.log('Creating new user account for consultation booking:', data.email);

        // Generate a temporary password (user will need to reset it)
        const tempPassword = `temp_${uuidv4().substring(0, 8)}`;

        const { data: newUser, error: userError } = await serviceSupabase.auth.admin.createUser({
          email: data.email,
          password: tempPassword,
          email_confirm: true, // Auto-confirm email for consultation users
          user_metadata: {
            full_name: `${data.firstName} ${data.lastName}`,
            role: 'student',
            signup_context: 'consultation',
            phone: data.phoneNumber,
          }
        });

        if (userError) {
          console.error('Error creating user account:', userError);
          // Continue without user account - consultation can still be created
        } else {
          userId = newUser.user?.id;
          console.log('Created user account:', userId);
        }
      } else {
        userId = userExists.id;
        console.log('Using existing user account:', userId);
      }

      // Step 3: Create consultation with user_id if available
      const consultationWithUser = {
        ...consultationData,
        user_id: userId
      };

      // Try to insert into database
      const { data: newConsultation, error } = await supabase
        .from('consultations')
        .insert(consultationWithUser)
        .select()
        .single();

      if (error) {
        // If there's a database error, log it but continue with fallback
        console.error('Database error creating consultation:', error);
        throw new Error(error.message);
      }

      // Revalidate cache for consultations
      revalidatePath('/consultation');

      // Return the consultation data with user info
      return {
        success: true,
        consultation: newConsultation,
        userCreated: !userExists,
        userId: userId
      };
    } catch (dbError) {
      // Fallback for development/testing - create a mock consultation
      console.log('Using fallback consultation creation mechanism');
      
      // Generate a mock consultation with UUID
      const mockConsultation = {
        id: uuidv4(),
        ...consultationData,
        user_id: null,
        payment_reference: null,
        payment_status: 'pending',
        payment_amount: null,
        updated_at: new Date().toISOString()
      };
      
      console.log('Created mock consultation:', mockConsultation);
      
      // Return the mock consultation
      return { success: true, consultation: mockConsultation };
    }
  } catch (error: any) {
    console.error('Error in createConsultation action:', error);
    return { 
      success: false, 
      error: error.message || 'An unexpected error occurred' 
    };
  }
}
