"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Check, Type, Eye, Zap } from "lucide-react";

const sampleTexts = {
  heading: "Tennis Whisperer",
  subheading: "Elevate Your Game with Premium Tennis Gear",
  paragraph: "Shop our professional rackets, balls, apparel & more. Trusted by amateurs and pros alike. We're committed to providing the highest quality tennis equipment with exceptional service.",
  button: "Shop Collection",
  price: "R 2,499.99",
  navigation: ["Home", "Shop", "Mentorship", "Blog", "Contact"]
};

interface FontOption {
  name: string;
  className: string;
  description: string;
  characteristics: string[];
  bestFor: string[];
  pros: string[];
  cons: string[];
}

const fontOptions: FontOption[] = [
  {
    name: "Outfit",
    className: "font-outfit",
    description: "Modern geometric sans-serif with clean lines and excellent readability",
    characteristics: ["Geometric", "Clean", "Modern", "Versatile"],
    bestFor: ["Headings", "UI Elements", "Modern Brands", "Digital Interfaces"],
    pros: [
      "Excellent readability at all sizes",
      "Modern and professional appearance",
      "Great for sports/fitness brands",
      "Wide range of weights available",
      "Good performance on screens"
    ],
    cons: [
      "Less personality than custom fonts",
      "Widely used (less unique)",
      "May feel too corporate for some contexts"
    ]
  },
  {
    name: "Inter",
    className: "font-inter",
    description: "Carefully crafted for computer screens with optimized letter spacing",
    characteristics: ["Optimized", "Readable", "Technical", "Precise"],
    bestFor: ["Body Text", "Data Display", "Technical Content", "Long Reading"],
    pros: [
      "Exceptional screen readability",
      "Optimized for digital interfaces",
      "Great for body text and data",
      "Excellent letter spacing",
      "Professional and trustworthy"
    ],
    cons: [
      "Less distinctive for branding",
      "Can feel too technical",
      "May lack warmth for lifestyle brands"
    ]
  }
];

export default function FontComparison() {
  const [selectedFont, setSelectedFont] = useState<string>("outfit");

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-8">
      <div className="text-center space-y-4">
        <h1 className="text-4xl font-bold">Font Selection for Tennis Whisperer</h1>
        <p className="text-muted-foreground max-w-2xl mx-auto">
          Compare Outfit vs Inter fonts to determine the best typography for our tennis e-commerce platform.
        </p>
      </div>

      {/* Font Options */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {fontOptions.map((font) => (
          <Card 
            key={font.name} 
            className={`cursor-pointer transition-all duration-300 ${
              selectedFont === font.name.toLowerCase() 
                ? 'ring-2 ring-primary border-primary' 
                : 'hover:shadow-lg'
            }`}
            onClick={() => setSelectedFont(font.name.toLowerCase())}
          >
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <Type className="h-5 w-5" />
                  {font.name}
                </CardTitle>
                {selectedFont === font.name.toLowerCase() && (
                  <Badge className="bg-primary text-primary-foreground">
                    <Check className="h-3 w-3 mr-1" />
                    Selected
                  </Badge>
                )}
              </div>
              <CardDescription>{font.description}</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Font Preview */}
              <div className={`space-y-3 p-4 bg-muted/30 rounded-lg ${font.className}`}>
                <h2 className="text-2xl font-bold">{sampleTexts.heading}</h2>
                <h3 className="text-lg font-semibold">{sampleTexts.subheading}</h3>
                <p className="text-sm">{sampleTexts.paragraph}</p>
                <div className="flex items-center gap-4">
                  <Button size="sm">{sampleTexts.button}</Button>
                  <span className="text-lg font-bold text-primary">{sampleTexts.price}</span>
                </div>
                <div className="flex gap-2 text-sm">
                  {sampleTexts.navigation.map((item) => (
                    <span key={item} className="px-2 py-1 bg-background rounded">
                      {item}
                    </span>
                  ))}
                </div>
              </div>

              {/* Characteristics */}
              <div>
                <h4 className="font-semibold mb-2">Characteristics</h4>
                <div className="flex flex-wrap gap-1">
                  {font.characteristics.map((char) => (
                    <Badge key={char} variant="outline" className="text-xs">
                      {char}
                    </Badge>
                  ))}
                </div>
              </div>

              {/* Best For */}
              <div>
                <h4 className="font-semibold mb-2">Best For</h4>
                <div className="flex flex-wrap gap-1">
                  {font.bestFor.map((use) => (
                    <Badge key={use} variant="secondary" className="text-xs">
                      {use}
                    </Badge>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Detailed Comparison */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Eye className="h-5 w-5" />
            Detailed Analysis
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {fontOptions.map((font) => (
              <div key={font.name} className="space-y-4">
                <h3 className="text-lg font-semibold">{font.name}</h3>
                
                <div>
                  <h4 className="font-medium text-green-600 mb-2">Pros</h4>
                  <ul className="space-y-1 text-sm">
                    {font.pros.map((pro, index) => (
                      <li key={index} className="flex items-start gap-2">
                        <Check className="h-3 w-3 text-green-600 mt-0.5 flex-shrink-0" />
                        {pro}
                      </li>
                    ))}
                  </ul>
                </div>

                <div>
                  <h4 className="font-medium text-orange-600 mb-2">Considerations</h4>
                  <ul className="space-y-1 text-sm">
                    {font.cons.map((con, index) => (
                      <li key={index} className="flex items-start gap-2">
                        <span className="w-3 h-3 bg-orange-200 rounded-full mt-0.5 flex-shrink-0" />
                        {con}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Recommendation */}
      <Card className="border-primary/20 bg-gradient-to-r from-primary/5 to-primary/10">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-primary">
            <Zap className="h-5 w-5" />
            Recommendation for Tennis Whisperer
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-3">
            <h4 className="font-semibold">Primary Choice: Outfit</h4>
            <p className="text-sm text-muted-foreground">
              <strong>Outfit</strong> is the recommended primary font for Tennis Whisperer because:
            </p>
            <ul className="space-y-1 text-sm">
              <li className="flex items-start gap-2">
                <Check className="h-3 w-3 text-primary mt-0.5 flex-shrink-0" />
                Perfect for sports/fitness brands with its modern, energetic feel
              </li>
              <li className="flex items-start gap-2">
                <Check className="h-3 w-3 text-primary mt-0.5 flex-shrink-0" />
                Excellent readability for headings and UI elements
              </li>
              <li className="flex items-start gap-2">
                <Check className="h-3 w-3 text-primary mt-0.5 flex-shrink-0" />
                Maintains professionalism while feeling approachable
              </li>
              <li className="flex items-start gap-2">
                <Check className="h-3 w-3 text-primary mt-0.5 flex-shrink-0" />
                Works well with the neomorphism design aesthetic
              </li>
            </ul>
          </div>

          <div className="space-y-3">
            <h4 className="font-semibold">Secondary Use: Inter</h4>
            <p className="text-sm text-muted-foreground">
              <strong>Inter</strong> can be used for specific contexts:
            </p>
            <ul className="space-y-1 text-sm">
              <li className="flex items-start gap-2">
                <Check className="h-3 w-3 text-primary mt-0.5 flex-shrink-0" />
                Body text in technical sections (product specifications)
              </li>
              <li className="flex items-start gap-2">
                <Check className="h-3 w-3 text-primary mt-0.5 flex-shrink-0" />
                Data tables and admin dashboard interfaces
              </li>
              <li className="flex items-start gap-2">
                <Check className="h-3 w-3 text-primary mt-0.5 flex-shrink-0" />
                Long-form content in blog articles
              </li>
            </ul>
          </div>

          <div className="pt-4 border-t">
            <p className="text-sm font-medium">
              Current Implementation: ✅ Outfit is already set as the primary font, which aligns with our recommendation.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
