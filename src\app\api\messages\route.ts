import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { notificationService } from '@/lib/notification-service';

export async function GET(request: NextRequest) {
  try {
    const supabase = createClient();
    
    // Check if user is authenticated
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const recipientId = searchParams.get('recipient_id');
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');

    if (!recipientId) {
      return NextResponse.json({ error: 'recipient_id parameter is required' }, { status: 400 });
    }

    // Fetch messages between current user and recipient
    const { data: messages, error } = await supabase
      .from('messages')
      .select(`
        *,
        sender:users!messages_sender_id_fkey(full_name, email),
        recipient:users!messages_recipient_id_fkey(full_name, email)
      `)
      .or(`and(sender_id.eq.${user.id},recipient_id.eq.${recipientId}),and(sender_id.eq.${recipientId},recipient_id.eq.${user.id})`)
      .order('created_at', { ascending: true })
      .range(offset, offset + limit - 1);

    if (error) {
      console.error('Error fetching messages:', error);
      return NextResponse.json({ error: 'Failed to fetch messages' }, { status: 500 });
    }

    return NextResponse.json({
      messages: messages || [],
      hasMore: (messages?.length || 0) === limit
    });

  } catch (error) {
    console.error('Error in messages API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabase = createClient();
    
    // Check if user is authenticated
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { recipientId, content } = body;

    // Validate required fields
    if (!recipientId || !content?.trim()) {
      return NextResponse.json({ 
        error: 'recipientId and content are required' 
      }, { status: 400 });
    }

    // Get recipient information for notification
    const { data: recipient, error: recipientError } = await supabase
      .from('users')
      .select('full_name, email')
      .eq('id', recipientId)
      .single();

    if (recipientError) {
      console.error('Error fetching recipient:', recipientError);
      return NextResponse.json({ error: 'Recipient not found' }, { status: 404 });
    }

    // Get sender information
    const { data: sender, error: senderError } = await supabase
      .from('users')
      .select('full_name, email')
      .eq('id', user.id)
      .single();

    if (senderError) {
      console.error('Error fetching sender:', senderError);
      return NextResponse.json({ error: 'Sender not found' }, { status: 404 });
    }

    // Create the message
    const { data: message, error: messageError } = await supabase
      .from('messages')
      .insert({
        sender_id: user.id,
        recipient_id: recipientId,
        content: content.trim(),
        read: false
      })
      .select(`
        *,
        sender:users!messages_sender_id_fkey(full_name, email),
        recipient:users!messages_recipient_id_fkey(full_name, email)
      `)
      .single();

    if (messageError) {
      console.error('Error creating message:', messageError);
      return NextResponse.json({ error: 'Failed to send message' }, { status: 500 });
    }

    // Send notification to recipient
    try {
      const senderName = sender.full_name || sender.email;
      const messagePreview = content.trim();
      
      await notificationService.notifyNewMessage(recipientId, senderName, messagePreview);
    } catch (notificationError) {
      console.error('Error sending message notification:', notificationError);
      // Don't fail the message creation if notification fails
    }

    return NextResponse.json({
      success: true,
      message
    });

  } catch (error) {
    console.error('Error creating message:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function PATCH(request: NextRequest) {
  try {
    const supabase = createClient();
    
    // Check if user is authenticated
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { messageIds, read } = body;

    if (!messageIds || !Array.isArray(messageIds)) {
      return NextResponse.json({ error: 'messageIds array is required' }, { status: 400 });
    }

    // Mark messages as read (only messages sent to the current user)
    const { error } = await supabase
      .from('messages')
      .update({ read: read !== undefined ? read : true })
      .in('id', messageIds)
      .eq('recipient_id', user.id);

    if (error) {
      console.error('Error updating message read status:', error);
      return NextResponse.json({ error: 'Failed to update messages' }, { status: 500 });
    }

    return NextResponse.json({ success: true });

  } catch (error) {
    console.error('Error updating messages:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
