"use client";

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { TrendingUp, Clock, Users, Star, ArrowRight, BookOpen, ShoppingBag } from 'lucide-react';
import Link from 'next/link';
import { createClient } from '@/utils/supabase/client';

interface TrendingItem {
  id: string;
  title: string;
  slug: string;
  type: 'article' | 'product';
  views?: number;
  likes?: number;
  price?: number;
  category?: string;
  readTime?: number;
  image?: string;
}

export function TrendingContent() {
  const [trendingItems, setTrendingItems] = useState<TrendingItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const supabase = createClient();

  useEffect(() => {
    loadTrendingContent();
  }, []);

  const loadTrendingContent = async () => {
    try {
      setIsLoading(true);
      
      // Get trending articles and products
      const [articlesResponse, productsResponse] = await Promise.all([
        supabase
          .from('blog_articles')
          .select('id, title, slug, views, likes, read_time, blog_categories(name)')
          .eq('is_published', true)
          .order('views', { ascending: false })
          .limit(3),
        supabase
          .from('products')
          .select('id, name, slug, price, category')
          .eq('is_active', true)
          .eq('is_featured', true)
          .limit(2)
      ]);

      const articles = articlesResponse.data || [];
      const products = productsResponse.data || [];

      const trendingArticles: TrendingItem[] = articles.map(article => ({
        id: article.id,
        title: article.title,
        slug: article.slug,
        type: 'article' as const,
        views: article.views,
        likes: article.likes,
        category: Array.isArray(article.blog_categories) 
          ? article.blog_categories[0]?.name 
          : article.blog_categories?.name,
        readTime: article.read_time
      }));

      const trendingProducts: TrendingItem[] = products.map(product => ({
        id: product.id,
        title: product.name,
        slug: product.slug,
        type: 'product' as const,
        price: product.price,
        category: product.category
      }));

      // Combine and shuffle for variety
      const combined = [...trendingArticles, ...trendingProducts];
      setTrendingItems(combined.slice(0, 4));
    } catch (error) {
      console.error('Error loading trending content:', error);
      setTrendingItems([]);
    } finally {
      setIsLoading(false);
    }
  };

  const formatCurrency = (price: number) => {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR'
    }).format(price);
  };

  if (isLoading) {
    return (
      <Card className="bg-gradient-to-r from-primary to-primary/80 text-white">
        <CardContent className="p-8">
          <div className="animate-pulse">
            <div className="h-6 bg-white/20 rounded w-1/3 mb-4"></div>
            <div className="space-y-3">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="h-4 bg-white/20 rounded"></div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="bg-gradient-to-r from-primary to-primary/80 text-white neo-shadow">
      <CardContent className="p-8">
        <div className="flex items-center gap-3 mb-6">
          <TrendingUp className="h-6 w-6 text-yellow-300" />
          <h3 className="text-2xl font-bold">Trending Now</h3>
        </div>
        
        {trendingItems.length > 0 ? (
          <div className="space-y-4">
            {trendingItems.map((item, index) => (
              <Link
                key={item.id}
                href={item.type === 'article' ? `/blog/${item.slug}` : `/product/${item.id}`}
                className="block group"
              >
                <div className="flex items-center gap-4 p-3 rounded-lg bg-white/10 hover:bg-white/20 transition-colors">
                  <div className="flex-shrink-0">
                    <Badge variant="outline" className="bg-white/20 text-white border-white/30">
                      #{index + 1}
                    </Badge>
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <h4 className="font-medium text-white group-hover:text-yellow-200 transition-colors truncate">
                      {item.title}
                    </h4>
                    
                    <div className="flex items-center gap-3 mt-1 text-sm text-white/70">
                      {item.type === 'article' ? (
                        <>
                          <div className="flex items-center gap-1">
                            <BookOpen className="h-3 w-3" />
                            <span>Article</span>
                          </div>
                          {item.views && (
                            <div className="flex items-center gap-1">
                              <Users className="h-3 w-3" />
                              <span>{item.views.toLocaleString()} views</span>
                            </div>
                          )}
                          {item.readTime && (
                            <div className="flex items-center gap-1">
                              <Clock className="h-3 w-3" />
                              <span>{item.readTime} min read</span>
                            </div>
                          )}
                        </>
                      ) : (
                        <>
                          <div className="flex items-center gap-1">
                            <ShoppingBag className="h-3 w-3" />
                            <span>Product</span>
                          </div>
                          {item.price && (
                            <div className="flex items-center gap-1">
                              <Star className="h-3 w-3" />
                              <span>{formatCurrency(item.price)}</span>
                            </div>
                          )}
                        </>
                      )}
                      {item.category && (
                        <Badge variant="outline" className="bg-white/10 text-white border-white/20 text-xs">
                          {item.category}
                        </Badge>
                      )}
                    </div>
                  </div>
                  
                  <ArrowRight className="h-4 w-4 text-white/70 group-hover:text-white transition-colors" />
                </div>
              </Link>
            ))}
            
            <div className="pt-4 border-t border-white/20">
              <div className="flex gap-2">
                <Link href="/blog" className="flex-1">
                  <Button variant="outline" className="w-full bg-white/10 border-white/30 text-white hover:bg-white/20 min-h-[44px]">
                    <BookOpen className="h-4 w-4 mr-2" />
                    All Articles
                  </Button>
                </Link>
                <Link href="/shop" className="flex-1">
                  <Button variant="outline" className="w-full bg-white/10 border-white/30 text-white hover:bg-white/20 min-h-[44px]">
                    <ShoppingBag className="h-4 w-4 mr-2" />
                    All Products
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        ) : (
          <div className="text-center py-8">
            <TrendingUp className="h-12 w-12 mx-auto mb-4 text-white/50" />
            <h4 className="text-lg font-medium mb-2">No Trending Content</h4>
            <p className="text-white/70 mb-4">
              Check back soon for the latest trending articles and products.
            </p>
            <div className="flex gap-2">
              <Link href="/blog" className="flex-1">
                <Button variant="outline" className="w-full bg-white/10 border-white/30 text-white hover:bg-white/20 min-h-[44px]">
                  Browse Articles
                </Button>
              </Link>
              <Link href="/shop" className="flex-1">
                <Button variant="outline" className="w-full bg-white/10 border-white/30 text-white hover:bg-white/20 min-h-[44px]">
                  Browse Products
                </Button>
              </Link>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
