"use client";

import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { createBrowserClient } from '@supabase/ssr';
import { CheckCircle, XCircle, AlertCircle, Database, User, Play } from 'lucide-react';

export default function DatabaseSetupPage() {
  const [user, setUser] = useState<any>(null);
  const [setupStatus, setSetupStatus] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [step, setStep] = useState(0);

  useEffect(() => {
    loadUser();
  }, []);

  const loadUser = async () => {
    const supabase = createBrowserClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
    );
    
    const { data: { user } } = await supabase.auth.getUser();
    setUser(user);
  };

  const runCompleteSetup = async () => {
    if (!user) {
      alert('Please sign in first');
      return;
    }

    setLoading(true);
    setStep(0);

    const supabase = createBrowserClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
    );

    try {
      // Get the user's session for authentication
      const { data: { session } } = await supabase.auth.getSession();

      if (!session) {
        alert('Please sign in first');
        return;
      }

      // Call the API route that uses service role
      const response = await fetch('/api/database-setup', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`,
        },
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Setup failed');
      }

      const results = await response.json();
      setSetupStatus(results);

    } catch (error: any) {
      console.error('Setup failed:', error);
      setSetupStatus({
        tables: { success: false, error: error.message },
        programs: { success: false, error: 'Setup failed' },
        resources: { success: false, error: 'Setup failed' },
        mentor: { success: false, error: 'Setup failed' },
        enrollment: { success: false, error: 'Setup failed' },
        sessions: { success: false, error: 'Setup failed' },
      });
    } finally {
      setLoading(false);
      setStep(0);
    }
  };

  const steps = [
    'Checking database tables...',
    'Creating mentorship programs...',
    'Adding learning resources...',
    'Setting up mentor profile...',
    'Creating student enrollment...',
    'Adding sample sessions...'
  ];

  return (
    <div className="p-6 space-y-6 max-w-4xl mx-auto">
      <div className="text-center space-y-4">
        <Database className="h-16 w-16 mx-auto text-blue-600" />
        <h1 className="text-3xl font-bold">Student Dashboard Database Setup</h1>
        <p className="text-muted-foreground">
          This tool will set up the required database tables and sample data for the student dashboard.
        </p>
      </div>

      {user && (
        <Card className="p-4">
          <div className="flex items-center gap-3">
            <User className="h-5 w-5" />
            <div>
              <p className="font-medium">Current User</p>
              <p className="text-sm text-muted-foreground">{user.email}</p>
              <p className="text-xs text-muted-foreground">ID: {user.id}</p>
            </div>
          </div>
        </Card>
      )}

      <Alert>
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          <strong>Important:</strong> Before running this setup, please execute the SQL script 
          <code className="mx-1 px-2 py-1 bg-muted rounded">database-setup-complete.sql</code> 
          in your Supabase SQL Editor to create the required tables.
        </AlertDescription>
      </Alert>

      <div className="flex justify-center">
        <Button 
          onClick={runCompleteSetup} 
          disabled={loading || !user}
          size="lg"
          className="px-8"
        >
          {loading ? (
            <>
              <Play className="h-4 w-4 mr-2 animate-spin" />
              Setting up... ({step > 0 ? steps[step - 1] : 'Starting'})
            </>
          ) : (
            <>
              <Play className="h-4 w-4 mr-2" />
              Run Complete Setup
            </>
          )}
        </Button>
      </div>

      {setupStatus && (
        <div className="space-y-4">
          <h3 className="text-xl font-semibold">Setup Results</h3>
          
          {Object.entries(setupStatus).map(([key, result]: [string, any]) => (
            <Card key={key} className="p-4">
              <div className="flex items-center gap-3">
                {result.success ? (
                  <CheckCircle className="h-5 w-5 text-green-600" />
                ) : (
                  <XCircle className="h-5 w-5 text-red-600" />
                )}
                <div className="flex-1">
                  <p className="font-medium capitalize">{key.replace(/([A-Z])/g, ' $1')}</p>
                  {result.error && (
                    <p className="text-sm text-red-600 mt-1">{result.error}</p>
                  )}
                </div>
                <Badge variant={result.success ? "default" : "destructive"}>
                  {result.success ? "Success" : "Failed"}
                </Badge>
              </div>
            </Card>
          ))}

          {Object.values(setupStatus).every((result: any) => result.success) && (
            <Alert>
              <CheckCircle className="h-4 w-4" />
              <AlertDescription>
                <strong>Setup Complete!</strong> Your student dashboard should now work correctly. 
                Visit <a href="/student-dashboard" className="underline text-blue-600">/student-dashboard</a> to test it.
              </AlertDescription>
            </Alert>
          )}
        </div>
      )}
    </div>
  );
}
