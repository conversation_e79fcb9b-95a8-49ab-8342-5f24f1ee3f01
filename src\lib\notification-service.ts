import { createServiceRoleClient } from '@/utils/supabase/service';
import { emailService } from './email-service';

export interface CreateNotificationParams {
  userId: string;
  title: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error' | 'order' | 'message' | 'session' | 'system';
  actionUrl?: string;
  metadata?: Record<string, any>;
  expiresAt?: Date;
}

export interface BulkNotificationParams {
  userIds: string[];
  title: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error' | 'order' | 'message' | 'session' | 'system';
  actionUrl?: string;
  metadata?: Record<string, any>;
  expiresAt?: Date;
}

class NotificationService {
  private supabase = createServiceRoleClient();

  /**
   * Create a single notification for a user
   */
  async createNotification(params: CreateNotificationParams): Promise<boolean> {
    try {
      const { error } = await this.supabase
        .from('notifications')
        .insert({
          user_id: params.userId,
          title: params.title,
          message: params.message,
          type: params.type,
          action_url: params.actionUrl,
          metadata: params.metadata,
          expires_at: params.expiresAt?.toISOString(),
          read: false
        });

      if (error) {
        console.error('Error creating notification:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error creating notification:', error);
      return false;
    }
  }

  /**
   * Create notifications for multiple users
   */
  async createBulkNotifications(params: BulkNotificationParams): Promise<boolean> {
    try {
      const notifications = params.userIds.map(userId => ({
        user_id: userId,
        title: params.title,
        message: params.message,
        type: params.type,
        action_url: params.actionUrl,
        metadata: params.metadata,
        expires_at: params.expiresAt?.toISOString(),
        read: false
      }));

      const { error } = await this.supabase
        .from('notifications')
        .insert(notifications);

      if (error) {
        console.error('Error creating bulk notifications:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error creating bulk notifications:', error);
      return false;
    }
  }

  /**
   * Notify user about new order
   */
  async notifyOrderCreated(userId: string, orderId: string, orderTotal: number, orderData?: any): Promise<boolean> {
    const notificationSuccess = await this.createNotification({
      userId,
      title: 'Order Confirmed',
      message: `Your order #${orderId.slice(-8)} for R${orderTotal.toFixed(2)} has been confirmed and is being processed.`,
      type: 'order',
      actionUrl: `/account/orders/${orderId}`,
      metadata: { orderId, orderTotal }
    });

    // Send email confirmation if order data is provided
    if (orderData && orderData.shippingDetails) {
      try {
        await emailService.sendOrderConfirmation({
          orderId,
          customerName: orderData.shippingDetails.name || 'Customer',
          customerEmail: orderData.shippingDetails.email,
          orderTotal,
          items: orderData.items || [],
          shippingAddress: `${orderData.shippingDetails.address}, ${orderData.shippingDetails.city}, ${orderData.shippingDetails.postal_code}`,
          estimatedDelivery: this.calculateEstimatedDelivery()
        });
      } catch (emailError) {
        console.error('Error sending order confirmation email:', emailError);
      }
    }

    return notificationSuccess;
  }

  /**
   * Notify user about order status update
   */
  async notifyOrderStatusUpdate(userId: string, orderId: string, status: string, customerData?: any): Promise<boolean> {
    const statusMessages = {
      'processing': 'Your order is being processed',
      'shipped': 'Your order has been shipped',
      'delivered': 'Your order has been delivered',
      'cancelled': 'Your order has been cancelled'
    };

    const notificationSuccess = await this.createNotification({
      userId,
      title: 'Order Update',
      message: statusMessages[status as keyof typeof statusMessages] || `Order status updated to ${status}`,
      type: status === 'delivered' ? 'success' : status === 'cancelled' ? 'error' : 'info',
      actionUrl: `/account/orders/${orderId}`,
      metadata: { orderId, status }
    });

    // Send email update if customer data is provided
    if (customerData && customerData.email) {
      try {
        await emailService.sendOrderStatusUpdate(
          customerData.email,
          customerData.name || 'Customer',
          orderId,
          status,
          customerData.trackingNumber
        );
      } catch (emailError) {
        console.error('Error sending order status email:', emailError);
      }
    }

    return notificationSuccess;
  }

  /**
   * Notify user about new message
   */
  async notifyNewMessage(userId: string, senderName: string, messagePreview: string): Promise<boolean> {
    return this.createNotification({
      userId,
      title: `New message from ${senderName}`,
      message: messagePreview.length > 100 ? `${messagePreview.slice(0, 100)}...` : messagePreview,
      type: 'message',
      actionUrl: '/student-dashboard/chat',
      metadata: { senderName }
    });
  }

  /**
   * Notify user about upcoming session
   */
  async notifyUpcomingSession(userId: string, sessionDate: Date, mentorName: string): Promise<boolean> {
    const timeUntil = Math.ceil((sessionDate.getTime() - Date.now()) / (1000 * 60 * 60));
    
    return this.createNotification({
      userId,
      title: 'Upcoming Session',
      message: `You have a session with ${mentorName} in ${timeUntil} hours`,
      type: 'session',
      actionUrl: '/student-dashboard/schedule',
      metadata: { sessionDate: sessionDate.toISOString(), mentorName },
      expiresAt: sessionDate
    });
  }

  /**
   * Notify user about session cancellation
   */
  async notifySessionCancelled(userId: string, sessionDate: Date, mentorName: string, reason?: string): Promise<boolean> {
    return this.createNotification({
      userId,
      title: 'Session Cancelled',
      message: `Your session with ${mentorName} on ${sessionDate.toLocaleDateString()} has been cancelled${reason ? `: ${reason}` : ''}`,
      type: 'warning',
      actionUrl: '/student-dashboard/schedule',
      metadata: { sessionDate: sessionDate.toISOString(), mentorName, reason }
    });
  }

  /**
   * Notify user about low stock items
   */
  async notifyLowStock(userId: string, productName: string, stock: number): Promise<boolean> {
    return this.createNotification({
      userId,
      title: 'Low Stock Alert',
      message: `${productName} is running low (${stock} left). Order soon to avoid disappointment!`,
      type: 'warning',
      actionUrl: '/shop',
      metadata: { productName, stock }
    });
  }

  /**
   * Notify admins about new order
   */
  async notifyAdminsNewOrder(orderId: string, customerName: string, orderTotal: number): Promise<boolean> {
    // Get all admin users
    const { data: admins } = await this.supabase
      .from('users')
      .select('id')
      .eq('role', 'admin');

    if (!admins || admins.length === 0) return false;

    return this.createBulkNotifications({
      userIds: admins.map(admin => admin.id),
      title: 'New Order Received',
      message: `New order #${orderId.slice(-8)} from ${customerName} for R${orderTotal.toFixed(2)}`,
      type: 'order',
      actionUrl: `/admin/orders/${orderId}`,
      metadata: { orderId, customerName, orderTotal }
    });
  }

  /**
   * Notify admins about new user registration
   */
  async notifyAdminsNewUser(userName: string, userEmail: string, userRole: string): Promise<boolean> {
    const { data: admins } = await this.supabase
      .from('users')
      .select('id')
      .eq('role', 'admin');

    if (!admins || admins.length === 0) return false;

    return this.createBulkNotifications({
      userIds: admins.map(admin => admin.id),
      title: 'New User Registration',
      message: `${userName} (${userEmail}) has registered as a ${userRole}`,
      type: 'info',
      actionUrl: '/admin/users',
      metadata: { userName, userEmail, userRole }
    });
  }

  /**
   * Notify admins about system issues
   */
  async notifyAdminsSystemIssue(title: string, message: string, severity: 'warning' | 'error' = 'warning'): Promise<boolean> {
    const { data: admins } = await this.supabase
      .from('users')
      .select('id')
      .eq('role', 'admin');

    if (!admins || admins.length === 0) return false;

    return this.createBulkNotifications({
      userIds: admins.map(admin => admin.id),
      title,
      message,
      type: severity,
      actionUrl: '/admin/activity',
      metadata: { severity, timestamp: new Date().toISOString() }
    });
  }

  /**
   * Clean up expired notifications
   */
  async cleanupExpiredNotifications(): Promise<boolean> {
    try {
      const { error } = await this.supabase
        .from('notifications')
        .delete()
        .lt('expires_at', new Date().toISOString());

      if (error) {
        console.error('Error cleaning up expired notifications:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error cleaning up expired notifications:', error);
      return false;
    }
  }

  /**
   * Get notification statistics for admin dashboard
   */
  async getNotificationStats(): Promise<{
    totalSent: number;
    unreadCount: number;
    todayCount: number;
    weekCount: number;
  } | null> {
    try {
      const { data, error } = await this.supabase
        .from('notifications')
        .select('id, read, created_at');

      if (error) {
        console.error('Error getting notification stats:', error);
        return null;
      }

      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

      return {
        totalSent: data.length,
        unreadCount: data.filter(n => !n.read).length,
        todayCount: data.filter(n => new Date(n.created_at) >= today).length,
        weekCount: data.filter(n => new Date(n.created_at) >= weekAgo).length
      };
    } catch (error) {
      console.error('Error getting notification stats:', error);
      return null;
    }
  }

  /**
   * Calculate estimated delivery date (3-5 business days)
   */
  private calculateEstimatedDelivery(): string {
    const now = new Date();
    const deliveryDate = new Date(now.getTime() + 5 * 24 * 60 * 60 * 1000); // 5 days from now
    return deliveryDate.toLocaleDateString('en-ZA', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  }
}

export const notificationService = new NotificationService();
