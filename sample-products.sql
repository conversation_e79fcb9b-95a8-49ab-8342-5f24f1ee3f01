-- Sample Products for Tennis-Gear Platform
-- Run this in your Supabase SQL Editor to add sample products for testing

-- Insert sample products for different categories
INSERT INTO public.products (id, name, price, description, image, category, stock, status, features, rating, reviews) VALUES
-- Tennis Rackets
('prod-1', 'Wilson Pro Staff 97', 199.99, 'Professional tennis racket used by top players worldwide', 'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=400', 'Tennis Rackets', 15, 'In Stock', ARRAY['100 sq in head size', '315g weight', '16x19 string pattern'], 4.8, 124),
('prod-2', 'Babolat Pure Drive', 179.99, 'Power and control racket perfect for intermediate players', 'https://images.unsplash.com/photo-1554068865-24cecd4e34b8?w=400', 'Tennis Rackets', 22, 'In Stock', ARRAY['100 sq in head size', '300g weight', 'Cortex Pure Feel'], 4.6, 89),
('prod-3', 'Head Speed MP', 189.99, 'Versatile racket offering excellent spin and control', 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400', 'Tennis Rackets', 18, 'In Stock', ARRAY['100 sq in head size', '300g weight', 'Graphene 360+ technology'], 4.7, 156),

-- Tennis Balls
('prod-4', 'Wilson US Open Tennis Balls', 12.99, 'Official tournament tennis balls used in US Open', 'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=400', 'Tennis Balls', 50, 'In Stock', ARRAY['ITF approved', 'Extra duty felt', 'Can of 3 balls'], 4.5, 78),
('prod-5', 'Penn Championship Tennis Balls', 9.99, 'High-quality practice tennis balls for all court surfaces', 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400', 'Tennis Balls', 75, 'In Stock', ARRAY['Regular duty felt', 'Can of 3 balls', 'All court surface'], 4.3, 45),

-- Tennis Shoes
('prod-6', 'Nike Air Zoom Vapor X', 129.99, 'Lightweight tennis shoes with excellent court feel', 'https://images.unsplash.com/photo-1549298916-b41d501d3772?w=400', 'Tennis Shoes', 25, 'In Stock', ARRAY['Zoom Air cushioning', 'Durable rubber outsole', 'Breathable mesh upper'], 4.6, 92),
('prod-7', 'Adidas Barricade Court', 119.99, 'Durable tennis shoes built for aggressive players', 'https://images.unsplash.com/photo-1606107557195-0e29a4b5b4aa?w=400', 'Tennis Shoes', 30, 'In Stock', ARRAY['Adituff toe protection', 'Bounce midsole', 'Adiwear outsole'], 4.4, 67),

-- Tennis Apparel
('prod-8', 'Nike Dri-FIT Tennis Polo', 49.99, 'Moisture-wicking polo shirt for optimal performance', 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=400', 'Tennis Apparel', 40, 'In Stock', ARRAY['Dri-FIT technology', '100% polyester', 'Regular fit'], 4.2, 34),
('prod-9', 'Adidas Tennis Shorts', 39.99, 'Comfortable tennis shorts with moisture management', 'https://images.unsplash.com/photo-1489987707025-afc232f7ea0f?w=400', 'Tennis Apparel', 35, 'In Stock', ARRAY['Climalite fabric', 'Side pockets', 'Elastic waistband'], 4.1, 28),

-- Tennis Bags
('prod-10', 'Wilson Super Tour Tennis Bag', 89.99, 'Spacious tennis bag with multiple compartments', 'https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=400', 'Tennis Bags', 12, 'In Stock', ARRAY['Holds 6 rackets', 'Shoe compartment', 'Adjustable straps'], 4.5, 56),

-- Accessories
('prod-11', 'Wilson Pro Overgrip', 14.99, 'Premium overgrip for enhanced racket feel', 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400', 'Accessories', 100, 'In Stock', ARRAY['Pack of 3 grips', 'Tacky feel', 'Easy installation'], 4.3, 89),
('prod-12', 'Babolat Vibration Dampener', 7.99, 'Reduces string vibration for better feel', 'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=400', 'Accessories', 80, 'In Stock', ARRAY['Pack of 2 dampeners', 'Easy to install', 'Durable silicone'], 4.0, 23)

ON CONFLICT (id) DO NOTHING;

-- Update the created_at and updated_at timestamps
UPDATE public.products 
SET created_at = NOW() - (random() * interval '30 days'),
    updated_at = NOW() - (random() * interval '7 days')
WHERE id IN ('prod-1', 'prod-2', 'prod-3', 'prod-4', 'prod-5', 'prod-6', 'prod-7', 'prod-8', 'prod-9', 'prod-10', 'prod-11', 'prod-12');
