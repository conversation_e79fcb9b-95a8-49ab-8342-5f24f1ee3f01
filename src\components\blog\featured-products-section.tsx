"use client";

import { useState, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  ShoppingCart, 
  Heart, 
  Eye, 
  Star, 
  ArrowRight, 
  Sparkles,
  ShoppingBag
} from "lucide-react";
import Link from "next/link";
import Image from "next/image";
import { createClient } from "@/utils/supabase/client";
import { useCart } from "@/context/cart-context";

interface Product {
  id: string;
  name: string;
  price: number;
  image: string;
  images?: string[];
  category: string;
  rating?: number;
  reviews?: number;
  stock: number;
  description?: string;
  is_featured: boolean;
}

interface FeaturedProductsSectionProps {
  title?: string;
  subtitle?: string;
  className?: string;
}

export function FeaturedProductsSection({
  title = "Featured Tennis Gear",
  subtitle = "Discover our curated selection of premium tennis equipment",
  className = ""
}: FeaturedProductsSectionProps) {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [wishlistItems, setWishlistItems] = useState<string[]>([]);
  const { addToCart } = useCart();
  const supabase = createClient();

  useEffect(() => {
    fetchFeaturedProducts();
  }, []);

  const fetchFeaturedProducts = async () => {
    try {
      setLoading(true);
      
      // Fetch featured products from the database
      const { data, error } = await supabase
        .from('products')
        .select('*')
        .eq('is_featured', true)
        .eq('is_active', true)
        .limit(3)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching featured products:', error);
        return;
      }

      setProducts(data || []);
    } catch (error) {
      console.error('Error fetching products:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAddToCart = (product: Product) => {
    addToCart({
      id: product.id as unknown as number,
      name: product.name,
      price: product.price,
      image: product.image || (product.images && product.images.length > 0 ? product.images[0] : ''),
    });
  };

  const handleToggleWishlist = (productId: string) => {
    setWishlistItems(prev =>
      prev.includes(productId)
        ? prev.filter(id => id !== productId)
        : [...prev, productId]
    );
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR'
    }).format(price);
  };

  if (loading) {
    return (
      <section className={`py-16 bg-gradient-to-br from-background via-background to-primary/5 ${className}`}>
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <div className="h-8 bg-muted/50 rounded w-64 mx-auto mb-4 animate-pulse"></div>
            <div className="h-4 bg-muted/30 rounded w-96 mx-auto animate-pulse"></div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[1, 2, 3].map((i) => (
              <Card key={i} className="glass-effect border border-white/10 neo-shadow animate-pulse">
                <div className="h-48 bg-muted/50 rounded-t-2xl"></div>
                <CardContent className="p-6">
                  <div className="space-y-3">
                    <div className="h-4 bg-muted/50 rounded"></div>
                    <div className="h-3 bg-muted/30 rounded w-3/4"></div>
                    <div className="h-8 bg-muted/30 rounded"></div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>
    );
  }

  if (products.length === 0) {
    return null;
  }

  return (
    <section className={`py-16 bg-gradient-to-br from-background via-background to-primary/5 ${className}`}>
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="flex items-center justify-center mb-4">
            <Sparkles className="w-6 h-6 text-primary mr-2" />
            <Badge className="bg-primary/20 text-primary border-primary/30">
              Curated Selection
            </Badge>
          </div>
          
          <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
            {title}
          </h2>
          
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            {subtitle}
          </p>
        </div>

        {/* Products Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
          {products.map((product, index) => (
            <Card 
              key={product.id} 
              className="glass-effect border border-white/10 neo-shadow hover:neo-shadow-light transition-all duration-300 group overflow-hidden"
            >
              <div className="relative">
                <div className="relative h-48 overflow-hidden">
                  <Image
                    src={product.image || '/api/placeholder/400/300'}
                    alt={product.name}
                    fill
                    className="object-cover transition-transform duration-500 group-hover:scale-105"
                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                  />
                  
                  {/* Overlay with actions */}
                  <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-all duration-300 flex items-center justify-center opacity-0 group-hover:opacity-100">
                    <div className="flex gap-2">
                      <Button
                        size="sm"
                        variant="secondary"
                        className="bg-white/90 hover:bg-white text-black min-h-[36px]"
                        asChild
                      >
                        <Link href={`/product/${product.id}`}>
                          <Eye className="h-4 w-4 mr-1" />
                          View
                        </Link>
                      </Button>
                      <Button
                        size="sm"
                        variant="secondary"
                        onClick={() => handleToggleWishlist(product.id)}
                        className={`min-h-[36px] min-w-[36px] ${
                          wishlistItems.includes(product.id)
                            ? 'bg-red-500 hover:bg-red-600 text-white'
                            : 'bg-white/90 hover:bg-white text-black'
                        }`}
                      >
                        <Heart className={`h-4 w-4 ${wishlistItems.includes(product.id) ? 'fill-current' : ''}`} />
                      </Button>
                    </div>
                  </div>
                </div>

                {/* Category Badge */}
                <Badge className="absolute top-3 left-3 bg-primary/20 text-primary border-primary/30">
                  {product.category}
                </Badge>

                {/* Stock Status */}
                {product.stock <= 5 && product.stock > 0 && (
                  <Badge className="absolute top-3 right-3 bg-orange-500/20 text-orange-600 border-orange-500/30">
                    Only {product.stock} left
                  </Badge>
                )}
                {product.stock === 0 && (
                  <Badge className="absolute top-3 right-3 bg-red-500/20 text-red-600 border-red-500/30">
                    Out of Stock
                  </Badge>
                )}
              </div>
              
              <CardContent className="p-6">
                <div className="space-y-4">
                  {/* Product Info */}
                  <div>
                    <h3 className="text-lg font-semibold text-foreground group-hover:text-primary transition-colors line-clamp-2">
                      {product.name}
                    </h3>
                    {product.description && (
                      <p className="text-sm text-muted-foreground mt-1 line-clamp-2">
                        {product.description}
                      </p>
                    )}
                  </div>

                  {/* Rating */}
                  {product.rating && (
                    <div className="flex items-center gap-2">
                      <div className="flex items-center">
                        {[...Array(5)].map((_, i) => (
                          <Star
                            key={i}
                            className={`h-3 w-3 ${
                              i < Math.floor(product.rating || 0)
                                ? 'fill-yellow-400 text-yellow-400'
                                : 'text-gray-300'
                            }`}
                          />
                        ))}
                      </div>
                      <span className="text-xs text-muted-foreground">
                        {product.rating} ({product.reviews || 0} reviews)
                      </span>
                    </div>
                  )}
                  
                  {/* Price */}
                  <div className="flex items-center justify-between">
                    <div className="text-2xl font-bold text-primary">
                      {formatPrice(product.price)}
                    </div>
                  </div>
                  
                  {/* Action Buttons */}
                  <div className="flex gap-2">
                    <Button
                      className="flex-1 bg-primary hover:bg-primary/90 text-white min-h-[44px]"
                      onClick={() => handleAddToCart(product)}
                      disabled={product.stock === 0}
                    >
                      <ShoppingCart className="h-4 w-4 mr-2" />
                      {product.stock === 0 ? 'Out of Stock' : 'Add to Cart'}
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Call to Action */}
        <div className="text-center">
          <Button size="lg" className="bg-primary hover:bg-primary/90 text-white min-h-[44px]" asChild>
            <Link href="/shop">
              <ShoppingBag className="w-5 h-5 mr-2" />
              View All Products
            </Link>
          </Button>
        </div>
      </div>
    </section>
  );
}
