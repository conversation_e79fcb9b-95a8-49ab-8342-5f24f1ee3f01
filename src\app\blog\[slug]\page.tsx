"use client";

import { useState, useEffect } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { 
  Calendar, 
  User, 
  Eye, 
  Heart, 
  MessageSquare,
  Clock,
  ArrowLeft,
  Share2,
  Bookmark,
  Send
} from "lucide-react";
import Link from 'next/link';
import { createClient } from '@/utils/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { ProductShowcase } from '@/components/blog/product-showcase';

interface BlogPost {
  id: string;
  title: string;
  slug: string;
  excerpt: string;
  content: string;
  featured_image: string | null;
  is_featured: boolean;
  views: number;
  likes: number;
  comments_count: number;
  read_time: number;
  tags: string[];
  published_at: string;
  seo_title: string | null;
  seo_description: string | null;
  blog_categories?: {
    name: string;
    color: string;
    slug: string;
  };
  users?: {
    full_name: string;
  };
}

interface Comment {
  id: string;
  content: string;
  created_at: string;
  parent_id: string | null;
  users?: {
    full_name: string;
  };
}

interface ProductReference {
  reference_type: 'featured' | 'mentioned' | 'related';
  position: number;
  products: {
    id: string;
    name: string;
    price: number;
    image_url: string;
    slug: string;
    category: string;
    description: string;
  };
}

export default function BlogPostPage() {
  const params = useParams();
  const router = useRouter();
  const { toast } = useToast();
  const [post, setPost] = useState<BlogPost | null>(null);
  const [relatedPosts, setRelatedPosts] = useState<BlogPost[]>([]);
  const [comments, setComments] = useState<Comment[]>([]);
  const [productReferences, setProductReferences] = useState<ProductReference[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isLiked, setIsLiked] = useState(false);
  const [newComment, setNewComment] = useState('');
  const [isSubmittingComment, setIsSubmittingComment] = useState(false);
  const supabase = createClient();

  const slug = params.slug as string;

  useEffect(() => {
    if (slug) {
      fetchPost();
    }
  }, [slug]);

  const fetchPost = async () => {
    try {
      setIsLoading(true);
      
      const response = await fetch(`/api/blog/articles/${slug}`);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch post');
      }

      setPost(data.article);
      setRelatedPosts(data.relatedArticles || []);
      setComments(data.comments || []);
      
      // Transform product references
      const transformedReferences = (data.productReferences || []).map((ref: any) => ({
        reference_type: ref.reference_type,
        position: ref.position,
        products: ref.products
      }));
      setProductReferences(transformedReferences);

      // Check if user has liked this post
      const { data: { session } } = await supabase.auth.getSession();
      if (session && data.article) {
        const { data: like } = await supabase
          .from('blog_article_likes')
          .select('id')
          .eq('article_id', data.article.id)
          .eq('user_id', session.user.id)
          .single();
        
        setIsLiked(!!like);
      }

    } catch (error) {
      console.error('Error fetching post:', error);
      toast({
        title: "Error",
        description: "Failed to load article",
        variant: "destructive",
      });
      router.push('/blog');
    } finally {
      setIsLoading(false);
    }
  };

  const handleLike = async () => {
    try {
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) {
        toast({
          title: "Authentication required",
          description: "Please sign in to like articles",
          variant: "destructive",
        });
        return;
      }

      const response = await fetch(`/api/blog/articles/${slug}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action: 'like' }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to like post');
      }

      setIsLiked(data.liked);
      if (post) {
        setPost({ ...post, likes: data.likes });
      }

    } catch (error) {
      console.error('Error liking post:', error);
      toast({
        title: "Error",
        description: "Failed to like article",
        variant: "destructive",
      });
    }
  };

  const handleSubmitComment = async () => {
    if (!newComment.trim()) return;

    try {
      setIsSubmittingComment(true);
      
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) {
        toast({
          title: "Authentication required",
          description: "Please sign in to comment",
          variant: "destructive",
        });
        return;
      }

      const response = await fetch(`/api/blog/articles/${slug}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          action: 'comment',
          content: newComment.trim()
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to submit comment');
      }

      toast({
        title: "Comment submitted",
        description: data.message || "Your comment has been submitted for approval",
      });

      setNewComment('');
      
      // Refresh comments if comment was auto-approved
      if (data.comment?.is_approved) {
        fetchPost();
      }

    } catch (error) {
      console.error('Error submitting comment:', error);
      toast({
        title: "Error",
        description: "Failed to submit comment",
        variant: "destructive",
      });
    } finally {
      setIsSubmittingComment(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatNumber = (num: number) => {
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  if (isLoading) {
    return (
  <div className="relative min-h-screen overflow-hidden bg-background">
    <div className="absolute inset-0 bg-[radial-gradient(#e5e7eb_1px,transparent_1px)] [background-size:16px_16px] opacity-25 z-0" />

    <div className="relative z-10 bg-gradient-to-br from-background via-background to-primary/5 min-h-screen">
      <div className="container mx-auto px-4 py-12">
        <div className="max-w-4xl mx-auto space-y-6">
          <div className="h-8 bg-white/20 rounded animate-pulse"></div>
          <div className="h-64 bg-white/20 rounded-2xl animate-pulse"></div>
          <div className="space-y-3">
            <div className="h-4 bg-white/20 rounded animate-pulse"></div>
            <div className="h-4 bg-white/10 rounded animate-pulse w-3/4"></div>
            <div className="h-4 bg-white/10 rounded animate-pulse w-1/2"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
    );
  }

  if (!post) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-background via-background to-primary/5">
        <div className="container mx-auto px-4 py-12">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-2xl font-bold text-foreground mb-4">Article not found</h1>
            <p className="text-muted-foreground mb-6">The article you're looking for doesn't exist or has been removed.</p>
            <Button asChild className="bg-primary hover:bg-primary/90 text-primary-foreground min-h-[44px]">
              <Link href="/blog">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Blog
              </Link>
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-primary/5">
      <div className="container mx-auto px-4 py-12">
        <div className="max-w-4xl mx-auto">
          {/* Back Button */}
          <Button 
            asChild 
            variant="ghost" 
            className="mb-6 glass-effect border border-white/20 hover:glass-effect-subtle min-h-[44px]"
          >
            <Link href="/blog">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Blog
            </Link>
          </Button>

          {/* Article Header */}
          <Card className="glass-effect border border-white/10 neo-shadow mb-8">
            <CardContent className="p-8">
              <div className="space-y-6">
                {/* Category and Featured Badge */}
                <div className="flex items-center gap-2">
                  {post.blog_categories && (
                    <Badge 
                      style={{ 
                        backgroundColor: post.blog_categories.color + '20',
                        color: post.blog_categories.color,
                        borderColor: post.blog_categories.color + '50'
                      }}
                    >
                      {post.blog_categories.name}
                    </Badge>
                  )}
                  {post.is_featured && (
                    <Badge className="bg-primary/20 text-primary border-primary/30">
                      Featured
                    </Badge>
                  )}
                </div>

                {/* Title */}
                <h1 className="text-3xl md:text-4xl font-bold text-foreground leading-tight">
                  {post.title}
                </h1>

                {/* Excerpt */}
                <p className="text-lg text-muted-foreground leading-relaxed">
                  {post.excerpt}
                </p>

                {/* Meta Information */}
                <div className="flex flex-wrap items-center gap-6 text-sm text-muted-foreground">
                  <div className="flex items-center gap-2">
                    <User className="h-4 w-4" />
                    <span>{post.users?.full_name || 'Tennis Whisperer'}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4" />
                    <span>{formatDate(post.published_at)}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4" />
                    <span>{post.read_time} min read</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Eye className="h-4 w-4" />
                    <span>{formatNumber(post.views)} views</span>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex items-center gap-3">
                  <Button
                    onClick={handleLike}
                    variant="outline"
                    className={`glass-effect border-white/20 hover:glass-effect-subtle min-h-[44px] ${
                      isLiked ? 'text-red-400 border-red-400/30' : ''
                    }`}
                  >
                    <Heart className={`h-4 w-4 mr-2 ${isLiked ? 'fill-current' : ''}`} />
                    {post.likes} {post.likes === 1 ? 'Like' : 'Likes'}
                  </Button>
                  
                  <Button
                    variant="outline"
                    className="glass-effect border-white/20 hover:glass-effect-subtle min-h-[44px]"
                  >
                    <MessageSquare className="h-4 w-4 mr-2" />
                    {post.comments_count} {post.comments_count === 1 ? 'Comment' : 'Comments'}
                  </Button>
                  
                  <Button
                    variant="outline"
                    className="glass-effect border-white/20 hover:glass-effect-subtle min-h-[44px]"
                  >
                    <Share2 className="h-4 w-4 mr-2" />
                    Share
                  </Button>
                  
                  <Button
                    variant="outline"
                    className="glass-effect border-white/20 hover:glass-effect-subtle min-h-[44px]"
                  >
                    <Bookmark className="h-4 w-4 mr-2" />
                    Save
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Featured Image */}
          {post.featured_image && (
            <div className="mb-8">
              <img
                src={post.featured_image}
                alt={post.title}
                className="w-full h-64 md:h-96 object-cover rounded-2xl neo-shadow"
              />
            </div>
          )}

          {/* Article Content */}
          <Card className="glass-effect border border-white/10 neo-shadow mb-8">
            <CardContent className="p-8">
              <div 
                className="prose prose-lg max-w-none text-foreground prose-headings:text-foreground prose-p:text-foreground prose-strong:text-foreground prose-a:text-primary hover:prose-a:text-primary/80"
                dangerouslySetInnerHTML={{ __html: post.content.replace(/\n/g, '<br />') }}
              />
            </CardContent>
          </Card>

          {/* Product Showcase */}
          {productReferences.length > 0 && (
            <div className="mb-8">
              <ProductShowcase 
                references={productReferences.map(ref => ({
                  product: ref.products,
                  reference_type: ref.reference_type,
                  position: ref.position
                }))}
              />
            </div>
          )}

          {/* Tags */}
          {post.tags && post.tags.length > 0 && (
            <Card className="glass-effect border border-white/10 neo-shadow mb-8">
              <CardContent className="p-6">
                <h3 className="text-lg font-semibold text-foreground mb-4">Tags</h3>
                <div className="flex flex-wrap gap-2">
                  {post.tags.map((tag) => (
                    <Badge key={tag} variant="outline" className="border-white/20 text-muted-foreground">
                      #{tag}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Comments Section */}
          <Card className="glass-effect border border-white/10 neo-shadow mb-8">
            <CardHeader>
              <CardTitle className="text-xl font-semibold text-foreground">
                Comments ({post.comments_count})
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Add Comment Form */}
              <div className="space-y-4">
                <h4 className="font-medium text-foreground">Leave a Comment</h4>
                <Textarea
                  placeholder="Share your thoughts..."
                  value={newComment}
                  onChange={(e) => setNewComment(e.target.value)}
                  className="glass-effect border-white/20 text-foreground placeholder:text-muted-foreground min-h-[100px]"
                />
                <Button
                  onClick={handleSubmitComment}
                  disabled={!newComment.trim() || isSubmittingComment}
                  className="bg-primary hover:bg-primary/90 text-primary-foreground min-h-[44px]"
                >
                  <Send className="h-4 w-4 mr-2" />
                  {isSubmittingComment ? 'Submitting...' : 'Submit Comment'}
                </Button>
              </div>

              {/* Comments List */}
              <div className="space-y-4">
                {comments.length > 0 ? (
                  comments.map((comment) => (
                    <div key={comment.id} className="glass-effect border border-white/10 p-4 rounded-xl">
                      <div className="flex items-start justify-between mb-2">
                        <div className="flex items-center gap-2">
                          <span className="font-medium text-foreground">
                            {comment.users?.full_name || 'Anonymous'}
                          </span>
                          <span className="text-sm text-muted-foreground">
                            {formatDate(comment.created_at)}
                          </span>
                        </div>
                      </div>
                      <p className="text-foreground leading-relaxed">{comment.content}</p>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    <MessageSquare className="h-8 w-8 mx-auto mb-2 opacity-50" />
                    <p>No comments yet. Be the first to share your thoughts!</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Related Posts */}
          {relatedPosts.length > 0 && (
            <Card className="glass-effect border border-white/10 neo-shadow">
              <CardHeader>
                <CardTitle className="text-xl font-semibold text-foreground">Related Articles</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  {relatedPosts.map((relatedPost) => (
                    <Card key={relatedPost.id} className="glass-effect border border-white/10 neo-shadow hover:neo-shadow-light transition-all duration-300 group">
                      <div className="relative">
                        <img
                          src={relatedPost.featured_image || '/api/placeholder/400/200'}
                          alt={relatedPost.title}
                          className="w-full h-32 object-cover rounded-t-2xl"
                        />
                      </div>
                      <CardContent className="p-4">
                        <h4 className="font-medium text-foreground group-hover:text-primary transition-colors line-clamp-2 mb-2">
                          {relatedPost.title}
                        </h4>
                        <p className="text-sm text-muted-foreground line-clamp-2 mb-3">
                          {relatedPost.excerpt}
                        </p>
                        <Button asChild variant="outline" size="sm" className="w-full glass-effect border-white/20 hover:glass-effect-subtle min-h-[36px]">
                          <Link href={`/blog/${relatedPost.slug}`}>
                            Read More
                          </Link>
                        </Button>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}
