"use client";

import { useState, useEffect } from "react";
import { User } from "@supabase/supabase-js";
import Footer from "@/components/footer";
import HeroBallpitCards from "@/components/demo/hero-ballpit-cards";
import Navbar from "@/components/navbar";
import { ArrowR<PERSON>, Award, BadgeCheck, Truck } from 'lucide-react';
import Image from "next/image";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { ProductCard } from "@/components/product-card";
import MentorshipPricing from "@/components/MentorshipPricing";
import CTASection from "@/components/cta-section";
import { useProducts } from "@/hooks/useProducts";
import { useCategories } from "@/hooks/useCategories";

interface HomePageClientProps {
  user: User | null;
}

interface Category {
  id: string;
  name: string;
  slug: string;
  image?: string;
  product_count: number;
}

// Category mapping for display
const categoryDisplayMap: Record<string, { image: string; displayName: string }> = {
  "tennis-rackets": {
    image: "https://images.unsplash.com/photo-1734459553318-1cde555f3c17?q=80&w=1527&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
    displayName: "Rackets"
  },
  "tennis-apparel": {
    image: "https://images.unsplash.com/photo-1618354691792-d1d42acfd860?w=800&q=80",
    displayName: "Apparel"
  },
  "accessories": {
    image: "/images/accessories.png",
    displayName: "Accessories"
  },
  "tennis-shoes": {
    image: "https://images.unsplash.com/photo-1606107557195-0e29a4b5b4aa?w=800&q=80",
    displayName: "Tennis Shoes"
  },
  "tennis-balls": {
    image: "https://images.unsplash.com/photo-1592709823125-a191f07a2a5e?w=800&q=80",
    displayName: "Tennis Balls"
  },
  "tennis-bags": {
    image: "https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=800&q=80",
    displayName: "Tennis Bags"
  }
};

export default function HomePageClient({ user }: HomePageClientProps) {
  const [currentCategoryIndex, setCurrentCategoryIndex] = useState(0);
  
  // Fetch products and categories using React Query hooks
  const { data: products = [], isLoading: productsLoading } = useProducts({ limit: 4 });
  const { data: categoriesData = [], isLoading: categoriesLoading } = useCategories();

  // Filter categories to show only those with products and map display data
  const displayCategories = categoriesData
    .filter((cat: Category) => cat.product_count > 0)
    .slice(0, 6) // Limit to 6 categories for carousel
    .map((cat: Category) => ({
      ...cat,
      displayName: categoryDisplayMap[cat.slug]?.displayName || cat.name,
      image: categoryDisplayMap[cat.slug]?.image || "/images/default-category.png"
    }));

  // Auto-rotate categories every 4 seconds
  useEffect(() => {
    if (displayCategories.length <= 3) return; // No need to rotate if 3 or fewer categories
    
    const interval = setInterval(() => {
      setCurrentCategoryIndex((prev) => (prev + 1) % (displayCategories.length - 2));
    }, 4000);

    return () => clearInterval(interval);
  }, [displayCategories.length]);

  // Get 3 categories to display (current + next 2)
  const visibleCategories = displayCategories.length > 3 
    ? displayCategories.slice(currentCategoryIndex, currentCategoryIndex + 3)
    : displayCategories;

  return (
    <div className="min-h-screen bg-background">
      <Navbar />
      <HeroBallpitCards />

      {/* Categories Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row justify-between items-center mb-12">
            <div>
              <h2 className="text-3xl font-bold text-foreground mb-2">Shop by Category</h2>
              <p className="text-muted-foreground">Find the perfect equipment for your game</p>
            </div>
            <Button asChild variant="outline" className="mt-4 md:mt-0">
              <Link href="/shop">
                View All Categories
                <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          </div>

          {categoriesLoading ? (
            <div className="grid md:grid-cols-3 gap-8">
              {[1, 2, 3].map((i) => (
                <div key={i} className="aspect-[4/3] bg-muted rounded-2xl animate-pulse" />
              ))}
            </div>
          ) : (
            <div className="grid md:grid-cols-3 gap-8">
              {visibleCategories.map((category) => (
                <Link
                  key={category.id}
                  href={`/shop?category=${category.slug}`}
                  className="group relative overflow-hidden rounded-2xl"
                >
                  <div className="aspect-[4/3] relative overflow-hidden rounded-2xl">
                    <Image
                      src={category.image}
                      alt={category.displayName}
                      fill
                      className="object-cover transition-transform duration-300 group-hover:scale-105"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
                    <div className="absolute bottom-0 left-0 p-6 text-white">
                      <h3 className="text-xl font-semibold mb-1">{category.displayName}</h3>
                      <p className="text-white/80 text-sm">{category.product_count} products</p>
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          )}
        </div>
      </section>

      {/* Featured Products Section */}
      <section className="py-20 bg-muted/30" id="featured">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row justify-between items-center mb-12">
            <div>
              <h2 className="text-3xl font-bold text-foreground mb-2">Featured Products</h2>
              <p className="text-muted-foreground">Our most popular items chosen by tennis enthusiasts</p>
            </div>
            <Button asChild variant="outline" className="mt-4 md:mt-0">
              <Link href="/shop">
                View All Products
                <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          </div>

          {productsLoading ? (
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
              {[1, 2, 3, 4].map((i) => (
                <div key={i} className="bg-muted rounded-2xl h-80 animate-pulse" />
              ))}
            </div>
          ) : (
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
              {products.slice(0, 4).map((product) => (
                <ProductCard key={product.id} product={product} />
              ))}
            </div>
          )}
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-foreground mb-4">Why Choose Tennis Gear</h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">We're committed to providing the highest quality tennis equipment with exceptional service</p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {[
              {
                icon: <Award className="w-10 h-10" />,
                title: "Premium Quality",
                description: "We source only the highest quality products from trusted manufacturers"
              },
              {
                icon: <BadgeCheck className="w-10 h-10" />,
                title: "Expert Selection",
                description: "Our team of tennis experts carefully selects each product in our catalog"
              },
              {
                icon: <Truck className="w-10 h-10" />,
                title: "Fast Delivery",
                description: "Enjoy quick shipping and hassle-free returns on all orders"
              }
            ].map((feature, index) => (
              <div key={index} className="p-8 bg-background rounded-2xl border border-border">
                <div className="text-primary mb-5 bg-primary/10 w-16 h-16 rounded-full flex items-center justify-center">{feature.icon}</div>
                <h3 className="text-xl font-semibold text-foreground mb-3">{feature.title}</h3>
                <p className="text-muted-foreground">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Mentorship Pricing Section */}
      <div id="mentorship">
        <MentorshipPricing />
      </div>

      {/* New CTA Section */}
      <CTASection />

      {/* <Footer /> */}
    </div>
  );
}
