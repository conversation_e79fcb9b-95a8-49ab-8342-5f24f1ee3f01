import { NextResponse } from 'next/server';
import { createClient } from '../../../utils/supabase/server';
import { createServiceClient } from '../../../utils/supabase/service';

export async function POST(request: Request) {
  try {
    // Verify user is authenticated
    const supabase = await createClient();
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Use service client for database operations to bypass RLS
    const serviceSupabase = createServiceClient();

    const results = {
      tables: { success: false, error: '' },
      programs: { success: false, error: '' },
      resources: { success: false, error: '' },
      mentor: { success: false, error: '' },
      enrollment: { success: false, error: '' },
      sessions: { success: false, error: '' },
    };

    try {
      // Step 1: Check if tables exist
      const { data, error } = await serviceSupabase.from('mentorship_programs').select('id').limit(1);
      if (!error) {
        results.tables.success = true;
      } else {
        results.tables.error = 'Tables may not exist. Please run the SQL script in Supabase dashboard.';
      }
    } catch (err: any) {
      results.tables.error = err.message;
    }

    // Step 2: Insert sample programs
    try {
      const { error: programError } = await serviceSupabase
        .from('mentorship_programs')
        .upsert([
          {
            id: '550e8400-e29b-41d4-a716-446655440001',
            name: '6-Month Tennis Mastery',
            description: 'Comprehensive tennis training program for beginners to intermediate players',
            duration_months: 6,
            price_monthly: 299.99,
            price_upfront: 1599.99,
            features: JSON.stringify(["Weekly 1-on-1 sessions", "Video analysis", "Training plans", "Progress tracking"])
          },
          {
            id: '550e8400-e29b-41d4-a716-************',
            name: '12-Month Pro Development',
            description: 'Advanced tennis coaching for competitive players',
            duration_months: 12,
            price_monthly: 399.99,
            price_upfront: 4199.99,
            features: JSON.stringify(["Bi-weekly sessions", "Tournament preparation", "Mental coaching", "Nutrition guidance"])
          }
        ]);

      if (programError) {
        results.programs.error = programError.message;
      } else {
        results.programs.success = true;
      }
    } catch (err: any) {
      results.programs.error = err.message;
    }

    // Step 3: Insert sample resources
    try {
      const { error: resourceError } = await serviceSupabase
        .from('resources')
        .upsert([
          {
            id: '550e8400-e29b-41d4-a716-************',
            title: 'Tennis Fundamentals Video Series',
            description: 'Complete video series covering basic tennis techniques and fundamentals',
            type: 'video',
            category: 'Fundamentals',
            format: 'mp4',
            file_path: 'resources/tennis-fundamentals.mp4',
            size_bytes: 524288000,
            download_count: 45,
            created_by: user.id
          },
          {
            id: '550e8400-e29b-41d4-a716-************',
            title: 'Serve Technique Guide',
            description: 'Comprehensive PDF guide to improving your tennis serve',
            type: 'document',
            category: 'Technique',
            format: 'pdf',
            file_path: 'resources/serve-guide.pdf',
            size_bytes: 2048000,
            download_count: 32,
            created_by: user.id
          },
          {
            id: '550e8400-e29b-41d4-a716-************',
            title: 'Weekly Training Plan',
            description: 'Structured training program for intermediate players',
            type: 'training',
            category: 'Training Plans',
            format: 'pdf',
            file_path: 'resources/weekly-plan.pdf',
            size_bytes: 1024000,
            download_count: 28,
            created_by: user.id
          }
        ]);

      if (resourceError) {
        results.resources.error = resourceError.message;
      } else {
        results.resources.success = true;
      }
    } catch (err: any) {
      results.resources.error = err.message;
    }

    // Step 4: Create mentor profile
    try {
      const { error: mentorError } = await serviceSupabase
        .from('mentors')
        .upsert([
          {
            id: '550e8400-e29b-41d4-a716-446655440003',
            user_id: user.id,
            bio: 'Professional tennis coach with 15 years of experience. Former college player and certified instructor.',
            specialties: JSON.stringify(["Serve technique", "Backhand improvement", "Mental game", "Tournament preparation"]),
            experience_years: 15,
            availability: JSON.stringify({
              "monday": ["09:00", "17:00"],
              "tuesday": ["09:00", "17:00"],
              "wednesday": ["09:00", "17:00"],
              "thursday": ["09:00", "17:00"],
              "friday": ["09:00", "17:00"]
            })
          }
        ]);

      if (mentorError) {
        results.mentor.error = mentorError.message;
      } else {
        results.mentor.success = true;
      }
    } catch (err: any) {
      results.mentor.error = err.message;
    }

    // Step 5: Create student enrollment
    try {
      const { error: enrollmentError } = await serviceSupabase
        .from('student_enrollments')
        .upsert([
          {
            id: '550e8400-e29b-41d4-a716-446655440008',
            student_id: user.id,
            program_id: '550e8400-e29b-41d4-a716-446655440001',
            mentor_id: '550e8400-e29b-41d4-a716-446655440003',
            start_date: '2025-01-01T00:00:00Z',
            end_date: '2025-07-01T00:00:00Z',
            payment_type: 'monthly',
            status: 'active'
          }
        ]);

      if (enrollmentError) {
        results.enrollment.error = enrollmentError.message;
      } else {
        results.enrollment.success = true;
      }
    } catch (err: any) {
      results.enrollment.error = err.message;
    }

    // Step 6: Create sample sessions
    try {
      const { error: sessionError } = await serviceSupabase
        .from('mentorship_sessions')
        .upsert([
          {
            id: '550e8400-e29b-41d4-a716-446655440009',
            enrollment_id: '550e8400-e29b-41d4-a716-446655440008',
            scheduled_at: '2025-01-25T10:00:00Z',
            duration_minutes: 60,
            status: 'scheduled',
            notes: 'Focus on serve technique and footwork'
          },
          {
            id: '550e8400-e29b-41d4-a716-44665544000a',
            enrollment_id: '550e8400-e29b-41d4-a716-446655440008',
            scheduled_at: '2025-01-18T10:00:00Z',
            duration_minutes: 60,
            status: 'completed',
            notes: 'Worked on backhand technique - great improvement shown'
          },
          {
            id: '550e8400-e29b-41d4-a716-44665544000b',
            enrollment_id: '550e8400-e29b-41d4-a716-446655440008',
            scheduled_at: '2025-02-01T10:00:00Z',
            duration_minutes: 60,
            status: 'scheduled',
            notes: 'Progress review and goal setting for next month'
          }
        ]);

      if (sessionError) {
        results.sessions.error = sessionError.message;
      } else {
        results.sessions.success = true;
      }
    } catch (err: any) {
      results.sessions.error = err.message;
    }

    return NextResponse.json(results);
  } catch (error: any) {
    console.error('Database setup error:', error);
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}
