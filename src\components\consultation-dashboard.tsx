"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Calendar,
  Clock,
  MapPin,
  Phone,
  Mail,
  User,
  ArrowRight,
  Star,
  CheckCircle,
  AlertCircle,
  CreditCard,
  Upgrade
} from "lucide-react";
import Link from "next/link";
import { createBrowserClient } from '@supabase/ssr';

interface Consultation {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
  phone_number: string;
  location: string;
  scheduled_date: string;
  scheduled_time: string;
  reason: string;
  status: 'pending' | 'confirmed' | 'cancelled' | 'completed';
  payment_status?: 'pending' | 'paid' | 'failed' | 'refunded';
  created_at: string;
}

interface ConsultationDashboardProps {
  userId: string;
  userEmail: string;
  userName: string;
}

export default function ConsultationDashboard({ userId, userEmail, userName }: ConsultationDashboardProps) {
  const [consultations, setConsultations] = useState<Consultation[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadConsultations();
  }, [userId, userEmail]);

  const loadConsultations = async () => {
    try {
      setLoading(true);
      const supabase = createBrowserClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
      );

      // Fetch consultations for this user (by user_id or email)
      const { data, error } = await supabase
        .from('consultations')
        .select('*')
        .or(`user_id.eq.${userId},email.eq.${userEmail}`)
        .order('created_at', { ascending: false });

      if (error) {
        throw error;
      }

      setConsultations(data || []);
    } catch (err: any) {
      console.error('Error loading consultations:', err);
      setError(err.message || 'Failed to load consultations');
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed': return 'bg-green-100 text-green-800';
      case 'completed': return 'bg-blue-100 text-blue-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      default: return 'bg-yellow-100 text-yellow-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'confirmed': return <CheckCircle className="h-4 w-4" />;
      case 'completed': return <CheckCircle className="h-4 w-4" />;
      case 'cancelled': return <AlertCircle className="h-4 w-4" />;
      default: return <Clock className="h-4 w-4" />;
    }
  };

  if (loading) {
    return (
      <div className="p-6 space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-muted rounded w-1/3 mb-4"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="h-64 bg-muted rounded"></div>
            <div className="h-64 bg-muted rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Welcome Header */}
      <div className="space-y-2">
        <h1 className="text-3xl font-bold">Welcome, {userName}!</h1>
        <p className="text-muted-foreground">
          Manage your consultation bookings and explore our mentorship programs.
        </p>
      </div>

      {/* Upgrade to Mentorship CTA */}
      <Card className="border-primary/20 bg-gradient-to-r from-primary/5 to-primary/10">
        <CardHeader>
          <div className="flex items-center gap-2">
            <Upgrade className="h-5 w-5 text-primary" />
            <CardTitle className="text-primary">Upgrade to Mentorship Program</CardTitle>
          </div>
          <CardDescription>
            Take your tennis game to the next level with our comprehensive mentorship programs.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div className="space-y-2">
              <h4 className="font-semibold">6-Month Program</h4>
              <p className="text-sm text-muted-foreground">Perfect for skill development</p>
              <div className="flex items-center gap-2">
                <Star className="h-4 w-4 text-yellow-500" />
                <span className="text-sm">Weekly 1-on-1 sessions</span>
              </div>
            </div>
            <div className="space-y-2">
              <h4 className="font-semibold">12-Month Program</h4>
              <p className="text-sm text-muted-foreground">Complete transformation journey</p>
              <div className="flex items-center gap-2">
                <Star className="h-4 w-4 text-yellow-500" />
                <span className="text-sm">Comprehensive training plan</span>
              </div>
            </div>
          </div>
          <Link href="/#mentorship">
            <Button className="w-full md:w-auto">
              Explore Mentorship Programs
              <ArrowRight className="h-4 w-4 ml-2" />
            </Button>
          </Link>
        </CardContent>
      </Card>

      {/* Consultations Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* My Consultations */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              My Consultations
            </CardTitle>
            <CardDescription>
              View and manage your consultation bookings
            </CardDescription>
          </CardHeader>
          <CardContent>
            {error ? (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            ) : consultations.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <Calendar className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>No consultations found</p>
                <p className="text-sm">Book your first consultation to get started</p>
                <Link href="/consultation/booking">
                  <Button className="mt-4">
                    Book Consultation
                  </Button>
                </Link>
              </div>
            ) : (
              <div className="space-y-4">
                {consultations.slice(0, 3).map((consultation) => (
                  <div key={consultation.id} className="border rounded-lg p-4 space-y-3">
                    <div className="flex items-center justify-between">
                      <Badge className={getStatusColor(consultation.status)}>
                        {getStatusIcon(consultation.status)}
                        <span className="ml-1 capitalize">{consultation.status}</span>
                      </Badge>
                      {consultation.payment_status && (
                        <Badge variant="outline" className="flex items-center gap-1">
                          <CreditCard className="h-3 w-3" />
                          {consultation.payment_status}
                        </Badge>
                      )}
                    </div>
                    
                    <div className="space-y-2 text-sm">
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                        <span>{new Date(consultation.scheduled_date).toLocaleDateString()}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Clock className="h-4 w-4 text-muted-foreground" />
                        <span>{consultation.scheduled_time}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <MapPin className="h-4 w-4 text-muted-foreground" />
                        <span className="truncate">{consultation.location}</span>
                      </div>
                    </div>
                    
                    <p className="text-sm text-muted-foreground line-clamp-2">
                      {consultation.reason}
                    </p>
                  </div>
                ))}
                
                {consultations.length > 3 && (
                  <Button variant="outline" className="w-full">
                    View All Consultations ({consultations.length})
                  </Button>
                )}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>
              Common actions for consultation users
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Link href="/consultation/booking">
              <Button className="w-full justify-start" variant="outline">
                <Calendar className="h-4 w-4 mr-2" />
                Book New Consultation
              </Button>
            </Link>
            
            <Link href="/#mentorship">
              <Button className="w-full justify-start" variant="outline">
                <Upgrade className="h-4 w-4 mr-2" />
                Upgrade to Mentorship
              </Button>
            </Link>
            
            <Link href="/shop">
              <Button className="w-full justify-start" variant="outline">
                <Star className="h-4 w-4 mr-2" />
                Shop Tennis Gear
              </Button>
            </Link>
            
            <Link href="/blog">
              <Button className="w-full justify-start" variant="outline">
                <User className="h-4 w-4 mr-2" />
                Read Tennis Tips
              </Button>
            </Link>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
